using System;
using System.Drawing;
using System.IO;
using System.Text.Json;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    /// <summary>
    /// 颜色配置测试工具 - 用于测试颜色保存和加载功能
    /// </summary>
    public class ColorConfigTest
    {
        /// <summary>
        /// 运行颜色配置测试
        /// </summary>
        public static void RunColorConfigTests()
        {
            Console.WriteLine("=== 颜色配置测试开始 ===");
            
            try
            {
                // 测试页面设置中的颜色配置
                TestPageSetupColors();
                
                // 测试PPT格式设置中的颜色配置
                TestPPTFormatColors();
                
                Console.WriteLine("=== 颜色配置测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 测试页面设置中的颜色配置
        /// </summary>
        private static void TestPageSetupColors()
        {
            Console.WriteLine("\n--- 测试页面设置颜色配置 ---");
            
            try
            {
                // 创建测试颜色设置
                var testColors = new BackgroundSettings
                {
                    BackgroundType = "SolidColor",
                    SolidColor = "#FF5733",  // 橙红色
                    GradientStartColor = "#3498DB",  // 蓝色
                    GradientEndColor = "#E74C3C",    // 红色
                    GradientDirection = "Vertical",
                    ImagePath = "",
                    ImageStretch = "Fit",
                    ImageTransparency = 80,
                    RemoveBackground = false
                };
                
                Console.WriteLine($"原始颜色设置:");
                Console.WriteLine($"  背景类型: {testColors.BackgroundType}");
                Console.WriteLine($"  纯色背景: {testColors.SolidColor}");
                Console.WriteLine($"  渐变起始: {testColors.GradientStartColor}");
                Console.WriteLine($"  渐变结束: {testColors.GradientEndColor}");
                Console.WriteLine($"  图片拉伸: {testColors.ImageStretch}");
                
                // 测试兼容性属性
                Console.WriteLine($"\n兼容性属性测试:");
                Console.WriteLine($"  BackgroundColor: {testColors.BackgroundColor}");
                Console.WriteLine($"  ImageFillMode: {testColors.ImageFillMode}");
                
                // 修改兼容性属性
                testColors.BackgroundColor = "#9B59B6";  // 紫色
                testColors.ImageFillMode = "Stretch";
                
                Console.WriteLine($"\n修改兼容性属性后:");
                Console.WriteLine($"  SolidColor: {testColors.SolidColor}");
                Console.WriteLine($"  ImageStretch: {testColors.ImageStretch}");
                
                // 测试序列化
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                string json = JsonSerializer.Serialize(testColors, jsonOptions);
                Console.WriteLine($"\n序列化结果:");
                Console.WriteLine(json);
                
                // 测试反序列化
                var deserializedColors = JsonSerializer.Deserialize<BackgroundSettings>(json, jsonOptions);
                if (deserializedColors != null)
                {
                    Console.WriteLine($"\n反序列化结果:");
                    Console.WriteLine($"  背景类型: {deserializedColors.BackgroundType}");
                    Console.WriteLine($"  纯色背景: {deserializedColors.SolidColor}");
                    Console.WriteLine($"  渐变起始: {deserializedColors.GradientStartColor}");
                    Console.WriteLine($"  渐变结束: {deserializedColors.GradientEndColor}");
                    Console.WriteLine($"  图片拉伸: {deserializedColors.ImageStretch}");
                    Console.WriteLine($"  兼容性BackgroundColor: {deserializedColors.BackgroundColor}");
                    Console.WriteLine($"  兼容性ImageFillMode: {deserializedColors.ImageFillMode}");
                    
                    // 验证颜色是否正确
                    bool colorsMatch = deserializedColors.SolidColor == testColors.SolidColor &&
                                     deserializedColors.GradientStartColor == testColors.GradientStartColor &&
                                     deserializedColors.GradientEndColor == testColors.GradientEndColor &&
                                     deserializedColors.ImageStretch == testColors.ImageStretch;
                    
                    Console.WriteLine($"\n颜色配置测试结果: {(colorsMatch ? "✓ 通过" : "❌ 失败")}");
                }
                else
                {
                    Console.WriteLine("❌ 反序列化失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 页面设置颜色测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试PPT格式设置中的颜色配置
        /// </summary>
        private static void TestPPTFormatColors()
        {
            Console.WriteLine("\n--- 测试PPT格式设置颜色配置 ---");
            
            try
            {
                // 测试简化配置结构
                var simplifiedConfig = new PPTFormatSettings
                {
                    IsEnabled = true,
                    GlobalSettings = new GlobalSettings
                    {
                        ApplyToAllSlides = true,
                        PreserveExistingFormatting = false
                    },
                    FontSettings = new FontSettings
                    {
                        DefaultFontName = "微软雅黑",
                        DefaultFontSize = 14,
                        ApplyToTitles = true,
                        ApplyToContent = true
                    },
                    ColorSettings = new ColorSettings
                    {
                        DefaultTextColor = "#2C3E50",
                        DefaultBackgroundColor = "#ECF0F1",
                        UseThemeColors = false
                    }
                };
                
                Console.WriteLine($"简化配置结构:");
                Console.WriteLine($"  启用状态: {simplifiedConfig.IsEnabled}");
                Console.WriteLine($"  兼容性EnablePPTFormatSettings: {simplifiedConfig.EnablePPTFormatSettings}");
                Console.WriteLine($"  默认文本颜色: {simplifiedConfig.ColorSettings?.DefaultTextColor}");
                Console.WriteLine($"  默认背景颜色: {simplifiedConfig.ColorSettings?.DefaultBackgroundColor}");
                
                // 测试序列化
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                string json = JsonSerializer.Serialize(simplifiedConfig, jsonOptions);
                Console.WriteLine($"\n序列化结果:");
                Console.WriteLine(json);
                
                // 测试反序列化
                var deserializedConfig = JsonSerializer.Deserialize<PPTFormatSettings>(json, jsonOptions);
                if (deserializedConfig != null)
                {
                    Console.WriteLine($"\n反序列化结果:");
                    Console.WriteLine($"  启用状态: {deserializedConfig.IsEnabled}");
                    Console.WriteLine($"  兼容性EnablePPTFormatSettings: {deserializedConfig.EnablePPTFormatSettings}");
                    Console.WriteLine($"  默认文本颜色: {deserializedConfig.ColorSettings?.DefaultTextColor}");
                    Console.WriteLine($"  默认背景颜色: {deserializedConfig.ColorSettings?.DefaultBackgroundColor}");
                    
                    // 验证配置是否正确
                    bool configMatch = deserializedConfig.IsEnabled == simplifiedConfig.IsEnabled &&
                                     deserializedConfig.ColorSettings?.DefaultTextColor == simplifiedConfig.ColorSettings?.DefaultTextColor &&
                                     deserializedConfig.ColorSettings?.DefaultBackgroundColor == simplifiedConfig.ColorSettings?.DefaultBackgroundColor;
                    
                    Console.WriteLine($"\nPPT格式设置测试结果: {(configMatch ? "✓ 通过" : "❌ 失败")}");
                }
                else
                {
                    Console.WriteLine("❌ 反序列化失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ PPT格式设置颜色测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 测试实际配置文件的加载
        /// </summary>
        public static void TestActualConfigFiles()
        {
            Console.WriteLine("\n--- 测试实际配置文件加载 ---");
            
            try
            {
                var configService = ConfigService.Instance;
                var config = configService.GetConfig();
                
                Console.WriteLine($"页面设置背景配置:");
                if (config.PageSetupSettings?.BackgroundSettings != null)
                {
                    var bg = config.PageSetupSettings.BackgroundSettings;
                    Console.WriteLine($"  背景类型: {bg.BackgroundType}");
                    Console.WriteLine($"  纯色背景: {bg.SolidColor}");
                    Console.WriteLine($"  渐变起始: {bg.GradientStartColor}");
                    Console.WriteLine($"  渐变结束: {bg.GradientEndColor}");
                    Console.WriteLine($"  图片拉伸: {bg.ImageStretch}");
                    Console.WriteLine($"  兼容性BackgroundColor: {bg.BackgroundColor}");
                    Console.WriteLine($"  兼容性ImageFillMode: {bg.ImageFillMode}");
                }
                else
                {
                    Console.WriteLine("  ❌ 背景设置为空");
                }
                
                Console.WriteLine($"\nPPT格式设置:");
                var pptFormat = configService.GetPPTFormatSettings();
                Console.WriteLine($"  启用状态: {pptFormat.IsEnabled}");
                Console.WriteLine($"  兼容性EnablePPTFormatSettings: {pptFormat.EnablePPTFormatSettings}");
                if (pptFormat.ColorSettings != null)
                {
                    Console.WriteLine($"  默认文本颜色: {pptFormat.ColorSettings.DefaultTextColor}");
                    Console.WriteLine($"  默认背景颜色: {pptFormat.ColorSettings.DefaultBackgroundColor}");
                }
                else
                {
                    Console.WriteLine($"  颜色设置为空，使用扁平结构");
                    Console.WriteLine($"  字体颜色: {pptFormat.FontColor}");
                    Console.WriteLine($"  背景颜色: {pptFormat.BackgroundColor}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 实际配置文件测试失败: {ex.Message}");
            }
        }
    }
}
