using System;
using System.Collections.Generic;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 应用程序主配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 路径设置
        /// </summary>
        public PathSettings PathSettings { get; set; } = new PathSettings();

        /// <summary>
        /// 处理设置
        /// </summary>
        public ProcessSettings ProcessSettings { get; set; } = new ProcessSettings();

        /// <summary>
        /// 功能启用状态
        /// </summary>
        public Dictionary<string, bool> FunctionEnabled { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// 日志设置
        /// </summary>
        public LogSettings LogSettings { get; set; } = new LogSettings();

        /// <summary>
        /// 窗体设置
        /// </summary>
        public FormSettings FormSettings { get; set; } = new FormSettings();

        /// <summary>
        /// 页面设置
        /// </summary>
        public PageSetupSettings PageSetupSettings { get; set; } = new PageSetupSettings();

        /// <summary>
        /// 背景设置
        /// </summary>
        public BackgroundSettings BackgroundSettings { get; set; } = new BackgroundSettings();

        /// <summary>
        /// 内容删除设置
        /// </summary>
        public ContentDeletionSettings ContentDeletionSettings { get; set; } = new ContentDeletionSettings();

        /// <summary>
        /// 内容替换设置
        /// </summary>
        public ContentReplacementSettings ContentReplacementSettings { get; set; } = new ContentReplacementSettings();

        /// <summary>
        /// 匹配段落格式设置
        /// </summary>
        public ParagraphFormatMatchingSettings ParagraphFormatMatchingSettings { get; set; } = new ParagraphFormatMatchingSettings();

        /// <summary>
        /// 页眉页脚设置
        /// </summary>
        public HeaderFooterSettings HeaderFooterSettings { get; set; } = new HeaderFooterSettings();

        /// <summary>
        /// 文档属性设置
        /// </summary>
        public DocumentPropertiesSettings DocumentPropertiesSettings { get; set; } = new DocumentPropertiesSettings();

        /// <summary>
        /// 文件名替换设置
        /// </summary>
        public FilenameReplacementSettings FilenameReplacementSettings { get; set; } = new FilenameReplacementSettings();

        /// <summary>
        /// PPT格式转换设置
        /// </summary>
        public PPTFormatConversionSettings PPTFormatConversionSettings { get; set; } = new PPTFormatConversionSettings();

        /// <summary>
        /// PPT格式设置
        /// </summary>
        public PPTFormatSettings? PPTFormatSettings { get; set; } = null;

        /// <summary>
        /// 定时设置
        /// </summary>
        public ScheduleSettings? ScheduleSettings { get; set; } = null;
    }

    /// <summary>
    /// 路径设置
    /// </summary>
    public class PathSettings
    {
        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubfolders { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;
    }

    /// <summary>
    /// 处理设置
    /// </summary>
    public class ProcessSettings
    {
        /// <summary>
        /// 是否复制文件（true=复制，false=移动）
        /// </summary>
        public bool CopyFiles { get; set; } = true;

        /// <summary>
        /// 是否直接处理源文件
        /// </summary>
        public bool ProcessSourceDirectly { get; set; } = false;

        /// <summary>
        /// 线程数 - 默认使用CPU核心数的一半，避免系统过载
        /// </summary>
        public int ThreadCount { get; set; } = Math.Max(1, Environment.ProcessorCount / 2);

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小 - 优化为较小值减少内存占用和UI更新频率
        /// </summary>
        public int BatchSize { get; set; } = 20;

        /// <summary>
        /// 全局冲突处理方式
        /// </summary>
        public FilenameConflictHandlingType GlobalConflictHandling { get; set; } = FilenameConflictHandlingType.Overwrite;

        /// <summary>
        /// 是否跳过临时文件 - 自动跳过以~$开头的Microsoft Office临时文件
        /// </summary>
        public bool SkipTemporaryFiles { get; set; } = true;

        /// <summary>
        /// 支持的文件格式 - 基于Aspose.Slides SaveFormat枚举的支持格式
        /// 包含PowerPoint和OpenDocument的主要格式
        /// </summary>
        public List<string> SupportedFormats { get; set; } = new List<string>
        {
            ".ppt", ".pptx", ".pptm", ".ppsx", ".ppsm", ".potx", ".potm", ".odp", ".otp"
        };
    }

    /// <summary>
    /// 日志设置
    /// </summary>
    public class LogSettings
    {
        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Info;

        /// <summary>
        /// 启用的日志类型
        /// </summary>
        public Dictionary<string, bool> EnabledLogTypes { get; set; } = new Dictionary<string, bool>
        {
            { "处理开始", true },
            { "处理完成", true },
            { "处理错误", true },
            { "文件操作", true },
            { "配置变更", false },
            { "调试信息", false },
            { "程序初始化", true },
            { "许可证", true },
            { "文件处理详情", true },
            { "配置加载", true },
            { "内容删除", true },
            { "性能统计", false },
            { "异常详情", true },
            { "系统状态", true },
            { "网络通信", false },
            { "用户操作", true },
            { "性能监控", false }
        };

        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 10;

        /// <summary>
        /// 保留日志文件天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// 窗体设置
    /// </summary>
    public class FormSettings
    {
        /// <summary>
        /// 窗体位置X
        /// </summary>
        public int LocationX { get; set; } = -1;

        /// <summary>
        /// 窗体位置Y
        /// </summary>
        public int LocationY { get; set; } = -1;

        /// <summary>
        /// 窗体宽度
        /// </summary>
        public int Width { get; set; } = 1024;

        /// <summary>
        /// 窗体高度
        /// </summary>
        public int Height { get; set; } = 768;

        /// <summary>
        /// 窗体状态
        /// </summary>
        public int WindowState { get; set; } = 0; // Normal
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    }

    /// <summary>
    /// 处理选项类
    /// </summary>
    public class ProcessingOptions
    {
        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubfolders { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;

        /// <summary>
        /// 是否复制文件（true=复制，false=移动）
        /// </summary>
        public bool CopyFiles { get; set; } = true;

        /// <summary>
        /// 是否直接处理源文件
        /// </summary>
        public bool ProcessSourceDirectly { get; set; } = false;

        /// <summary>
        /// 冲突处理方式
        /// </summary>
        public FilenameConflictHandlingType ConflictHandling { get; set; } = FilenameConflictHandlingType.Overwrite;

        /// <summary>
        /// 线程数 - 默认使用CPU核心数的一半，避免系统过载
        /// </summary>
        public int ThreadCount { get; set; } = Math.Max(1, Environment.ProcessorCount / 2);

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小 - 优化为较小值减少内存占用和UI更新频率
        /// </summary>
        public int BatchSize { get; set; } = 20;

        /// <summary>
        /// 支持的文件格式
        /// </summary>
        public List<string> SupportedFormats { get; set; } = new List<string>();
    }

    /// <summary>
    /// 处理统计信息类
    /// </summary>
    public class ProcessingStats
    {
        /// <summary>
        /// 总文件数
        /// </summary>
        public int TotalFiles { get; set; } = 0;

        /// <summary>
        /// 成功处理数
        /// </summary>
        public int SuccessCount { get; set; } = 0;

        /// <summary>
        /// 失败处理数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; } = null;

        /// <summary>
        /// 处理进度百分比
        /// </summary>
        public double ProgressPercentage => TotalFiles > 0 ? (double)(SuccessCount + FailureCount) / TotalFiles * 100 : 0;

        /// <summary>
        /// 成功率百分比
        /// </summary>
        public double SuccessRate => (SuccessCount + FailureCount) > 0 ? (double)SuccessCount / (SuccessCount + FailureCount) * 100 : 0;

        /// <summary>
        /// 处理耗时
        /// </summary>
        public TimeSpan ElapsedTime => (EndTime ?? DateTime.Now) - StartTime;

        /// <summary>
        /// 处理速度（文件/分钟）
        /// </summary>
        public double ProcessingSpeed
        {
            get
            {
                var elapsed = ElapsedTime.TotalMinutes;
                return elapsed > 0 ? (SuccessCount + FailureCount) / elapsed : 0;
            }
        }
    }

    /// <summary>
    /// 处理进度事件参数
    /// </summary>
    public class ProcessProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 进度百分比
        /// </summary>
        public int ProgressPercentage { get; }

        /// <summary>
        /// 处理统计信息
        /// </summary>
        public ProcessingStats Stats { get; }

        public ProcessProgressEventArgs(int progressPercentage, ProcessingStats stats)
        {
            ProgressPercentage = progressPercentage;
            Stats = stats;
        }
    }

    /// <summary>
    /// 处理完成事件参数
    /// </summary>
    public class ProcessCompletedEventArgs : EventArgs
    {
        /// <summary>
        /// 处理统计信息
        /// </summary>
        public ProcessingStats Stats { get; }

        /// <summary>
        /// 完成消息
        /// </summary>
        public string Message { get; }

        public ProcessCompletedEventArgs(ProcessingStats stats, string message)
        {
            Stats = stats;
            Message = message;
        }
    }

    /// <summary>
    /// 文件处理事件参数
    /// </summary>
    public class FileProcessedEventArgs : EventArgs
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; }

        public FileProcessedEventArgs(string fileName, bool isSuccess, string? errorMessage)
        {
            FileName = fileName;
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
        }
    }

    /// <summary>
    /// 页面设置配置类
    /// </summary>
    public class PageSetupSettings
    {
        /// <summary>
        /// 幻灯片宽度
        /// </summary>
        public float Width { get; set; } = 25.4f; // 默认25.4厘米

        /// <summary>
        /// 幻灯片高度
        /// </summary>
        public float Height { get; set; } = 19.05f; // 默认19.05厘米

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = "厘米";

        /// <summary>
        /// 是否横向（true=横向，false=纵向）
        /// </summary>
        public bool IsLandscape { get; set; } = true;

        /// <summary>
        /// 尺寸类型（Standard, Widescreen, Custom）
        /// </summary>
        public string SizeType { get; set; } = "Standard";

        /// <summary>
        /// 宽高比例
        /// </summary>
        public float AspectRatio { get; set; } = 4f / 3f;

        /// <summary>
        /// 是否锁定宽高比例
        /// </summary>
        public bool MaintainAspectRatio { get; set; } = true;

        /// <summary>
        /// 是否启用预设尺寸
        /// </summary>
        public bool EnablePresetSizes { get; set; } = true;

        /// <summary>
        /// 是否启用尺寸类型
        /// </summary>
        public bool EnableSizeType { get; set; } = true;

        /// <summary>
        /// 是否启用自定义尺寸
        /// </summary>
        public bool EnableCustomSize { get; set; } = true;

        /// <summary>
        /// 是否启用比例调整
        /// </summary>
        public bool EnableAspectRatioAdjustment { get; set; } = true;

        /// <summary>
        /// 当前选择的预设尺寸
        /// </summary>
        public string SelectedPresetSize { get; set; } = "";

        /// <summary>
        /// 是否应用到所有幻灯片
        /// </summary>
        public bool ApplyToAllSlides { get; set; } = true;

        /// <summary>
        /// 背景设置
        /// </summary>
        public BackgroundSettings BackgroundSettings { get; set; } = new BackgroundSettings();

        /// <summary>
        /// 获取尺寸描述
        /// </summary>
        /// <returns>尺寸描述字符串</returns>
        public string GetSizeDescription()
        {
            var orientation = IsLandscape ? "横向" : "纵向";
            return $"{Width} x {Height} {Unit} ({SizeType}, {orientation})";
        }

        /// <summary>
        /// 克隆设置
        /// </summary>
        /// <returns>克隆的页面设置</returns>
        public PageSetupSettings Clone()
        {
            return new PageSetupSettings
            {
                Width = this.Width,
                Height = this.Height,
                Unit = this.Unit,
                IsLandscape = this.IsLandscape,
                SizeType = this.SizeType,
                AspectRatio = this.AspectRatio,
                MaintainAspectRatio = this.MaintainAspectRatio,
                EnablePresetSizes = this.EnablePresetSizes,
                EnableSizeType = this.EnableSizeType,
                EnableCustomSize = this.EnableCustomSize,
                EnableAspectRatioAdjustment = this.EnableAspectRatioAdjustment,
                SelectedPresetSize = this.SelectedPresetSize,
                ApplyToAllSlides = this.ApplyToAllSlides,
                BackgroundSettings = this.BackgroundSettings.Clone()
            };
        }
    }

    /// <summary>
    /// 页眉页脚设置配置类
    /// </summary>
    public class HeaderFooterSettings
    {
        /// <summary>
        /// 是否启用页眉页脚设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 删除设置
        /// </summary>
        public HeaderFooterDeletionSettings DeletionSettings { get; set; } = new HeaderFooterDeletionSettings();

        /// <summary>
        /// 演示文稿级别设置
        /// </summary>
        public PresentationHeaderFooterSettings PresentationSettings { get; set; } = new PresentationHeaderFooterSettings();

        /// <summary>
        /// 普通幻灯片设置
        /// </summary>
        public SlideHeaderFooterSettings SlideSettings { get; set; } = new SlideHeaderFooterSettings();

        /// <summary>
        /// 母版幻灯片设置
        /// </summary>
        public MasterSlideHeaderFooterSettings MasterSlideSettings { get; set; } = new MasterSlideHeaderFooterSettings();

        /// <summary>
        /// 布局幻灯片设置
        /// </summary>
        public LayoutSlideHeaderFooterSettings LayoutSlideSettings { get; set; } = new LayoutSlideHeaderFooterSettings();

        /// <summary>
        /// 备注幻灯片设置
        /// </summary>
        public NotesSlideHeaderFooterSettings NotesSlideSettings { get; set; } = new NotesSlideHeaderFooterSettings();
    }

    /// <summary>
    /// 页眉页脚删除设置
    /// </summary>
    public class HeaderFooterDeletionSettings
    {
        /// <summary>
        /// 是否删除所有页脚
        /// </summary>
        public bool DeleteAllFooters { get; set; } = false;

        /// <summary>
        /// 是否删除所有页码
        /// </summary>
        public bool DeleteAllSlideNumbers { get; set; } = false;

        /// <summary>
        /// 是否删除所有日期时间
        /// </summary>
        public bool DeleteAllDateTimes { get; set; } = false;

        /// <summary>
        /// 删除范围类型（All, SelectedSlides, SlideRange）
        /// </summary>
        public string DeletionScope { get; set; } = "All";

        /// <summary>
        /// 指定幻灯片范围（当DeletionScope为SlideRange时使用）
        /// </summary>
        public string SlideRange { get; set; } = "1-";

        /// <summary>
        /// 选中的幻灯片索引列表（当DeletionScope为SelectedSlides时使用）
        /// </summary>
        public List<int> SelectedSlideIndexes { get; set; } = new List<int>();
    }

    /// <summary>
    /// 演示文稿级别页眉页脚设置
    /// </summary>
    public class PresentationHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用演示文稿级别设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚文本
        /// </summary>
        public string FooterText { get; set; } = "";

        /// <summary>
        /// 是否显示页脚
        /// </summary>
        public bool ShowFooter { get; set; } = false;

        /// <summary>
        /// 是否显示页码
        /// </summary>
        public bool ShowSlideNumber { get; set; } = false;

        /// <summary>
        /// 页码格式
        /// </summary>
        public string SlideNumberFormat { get; set; } = "幻灯片 #";

        /// <summary>
        /// 是否显示日期时间
        /// </summary>
        public bool ShowDateTime { get; set; } = false;

        /// <summary>
        /// 日期时间文本
        /// </summary>
        public string DateTimeText { get; set; } = "";

        /// <summary>
        /// 日期时间格式
        /// </summary>
        public string DateTimeFormat { get; set; } = "yyyy/MM/dd";

        /// <summary>
        /// 是否自动更新日期时间
        /// </summary>
        public bool AutoUpdateDateTime { get; set; } = true;
    }

    /// <summary>
    /// 普通幻灯片页眉页脚设置
    /// </summary>
    public class SlideHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用幻灯片级别设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚设置
        /// </summary>
        public FooterSettings Footer { get; set; } = new FooterSettings();

        /// <summary>
        /// 页码设置
        /// </summary>
        public SlideNumberSettings SlideNumber { get; set; } = new SlideNumberSettings();

        /// <summary>
        /// 日期时间设置
        /// </summary>
        public DateTimeSettings DateTime { get; set; } = new DateTimeSettings();

        /// <summary>
        /// 应用范围类型（AllSlides, SelectedSlides, SlideRange）
        /// </summary>
        public string ApplyScope { get; set; } = "AllSlides";

        /// <summary>
        /// 指定幻灯片范围（当ApplyScope为SlideRange时使用）
        /// </summary>
        public string SlideRange { get; set; } = "1-";

        /// <summary>
        /// 选中的幻灯片索引列表
        /// </summary>
        public List<int> SelectedSlideIndexes { get; set; } = new List<int>();


    }

    /// <summary>
    /// 页脚设置
    /// </summary>
    public class FooterSettings
    {
        /// <summary>
        /// 是否启用页脚
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚文本
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// 是否显示页脚
        /// </summary>
        public bool IsVisible { get; set; } = false;

        /// <summary>
        /// 是否应用到所有幻灯片
        /// </summary>
        public bool ApplyToAllSlides { get; set; } = true;
    }



    /// <summary>
    /// 页码设置
    /// </summary>
    public class SlideNumberSettings
    {
        /// <summary>
        /// 是否启用页码
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 是否显示页码
        /// </summary>
        public bool IsVisible { get; set; } = false;

        /// <summary>
        /// 页码格式
        /// </summary>
        public string Format { get; set; } = "#";

        /// <summary>
        /// 页码前缀
        /// </summary>
        public string Prefix { get; set; } = "";

        /// <summary>
        /// 页码后缀
        /// </summary>
        public string Suffix { get; set; } = "";

        /// <summary>
        /// 起始页码
        /// </summary>
        public int StartNumber { get; set; } = 1;
    }

    /// <summary>
    /// 日期时间设置
    /// </summary>
    public class DateTimeSettings
    {
        /// <summary>
        /// 是否启用日期时间
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 是否显示日期时间
        /// </summary>
        public bool IsVisible { get; set; } = false;

        /// <summary>
        /// 日期时间格式
        /// </summary>
        public string Format { get; set; } = "yyyy/MM/dd";

        /// <summary>
        /// 自定义日期时间文本
        /// </summary>
        public string CustomText { get; set; } = "";

        /// <summary>
        /// 是否自动更新
        /// </summary>
        public bool AutoUpdate { get; set; } = true;

        /// <summary>
        /// 是否使用自定义文本
        /// </summary>
        public bool UseCustomText { get; set; } = false;
    }

    /// <summary>
    /// 母版幻灯片页眉页脚设置
    /// </summary>
    public class MasterSlideHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用母版幻灯片设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚设置
        /// </summary>
        public FooterSettings Footer { get; set; } = new FooterSettings();

        /// <summary>
        /// 页码设置
        /// </summary>
        public SlideNumberSettings SlideNumber { get; set; } = new SlideNumberSettings();

        /// <summary>
        /// 日期时间设置
        /// </summary>
        public DateTimeSettings DateTime { get; set; } = new DateTimeSettings();

        /// <summary>
        /// 是否应用到所有子幻灯片
        /// </summary>
        public bool ApplyToChildSlides { get; set; } = true;
    }

    /// <summary>
    /// 布局幻灯片页眉页脚设置
    /// </summary>
    public class LayoutSlideHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用布局幻灯片设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚设置
        /// </summary>
        public FooterSettings Footer { get; set; } = new FooterSettings();

        /// <summary>
        /// 页码设置
        /// </summary>
        public SlideNumberSettings SlideNumber { get; set; } = new SlideNumberSettings();

        /// <summary>
        /// 日期时间设置
        /// </summary>
        public DateTimeSettings DateTime { get; set; } = new DateTimeSettings();

        /// <summary>
        /// 选中的布局幻灯片索引列表
        /// </summary>
        public List<int> SelectedLayoutIndexes { get; set; } = new List<int>();

        /// <summary>
        /// 是否应用到所有布局幻灯片
        /// </summary>
        public bool ApplyToAllLayouts { get; set; } = true;

        /// <summary>
        /// 是否应用到子幻灯片
        /// </summary>
        public bool ApplyToChildSlides { get; set; } = true;
    }

    /// <summary>
    /// 备注幻灯片页眉页脚设置
    /// </summary>
    public class NotesSlideHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用备注幻灯片设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 备注母版设置
        /// </summary>
        public NotesMasterHeaderFooterSettings NotesMaster { get; set; } = new NotesMasterHeaderFooterSettings();

        /// <summary>
        /// 备注幻灯片设置
        /// </summary>
        public NotesSlideIndividualSettings NotesSlide { get; set; } = new NotesSlideIndividualSettings();
    }

    /// <summary>
    /// 备注母版页眉页脚设置
    /// </summary>
    public class NotesMasterHeaderFooterSettings
    {
        /// <summary>
        /// 是否启用备注母版设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚设置
        /// </summary>
        public FooterSettings Footer { get; set; } = new FooterSettings();

        /// <summary>
        /// 页码设置
        /// </summary>
        public SlideNumberSettings SlideNumber { get; set; } = new SlideNumberSettings();

        /// <summary>
        /// 日期时间设置
        /// </summary>
        public DateTimeSettings DateTime { get; set; } = new DateTimeSettings();

        /// <summary>
        /// 是否应用到所有子备注幻灯片
        /// </summary>
        public bool ApplyToChildSlides { get; set; } = true;
    }

    /// <summary>
    /// 备注幻灯片个别设置
    /// </summary>
    public class NotesSlideIndividualSettings
    {
        /// <summary>
        /// 是否启用备注幻灯片个别设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 页脚设置
        /// </summary>
        public FooterSettings Footer { get; set; } = new FooterSettings();

        /// <summary>
        /// 页码设置
        /// </summary>
        public SlideNumberSettings SlideNumber { get; set; } = new SlideNumberSettings();

        /// <summary>
        /// 日期时间设置
        /// </summary>
        public DateTimeSettings DateTime { get; set; } = new DateTimeSettings();

        /// <summary>
        /// 应用范围类型
        /// </summary>
        public string ApplyScope { get; set; } = "AllNotesSlides";

        /// <summary>
        /// 指定备注幻灯片范围（当ApplyScope为SlideRange时使用）
        /// </summary>
        public string SlideRange { get; set; } = "1-";

        /// <summary>
        /// 选中的备注幻灯片索引列表
        /// </summary>
        public List<int> SelectedNotesSlideIndexes { get; set; } = new List<int>();
    }

    /// <summary>
    /// 背景设置配置类
    /// </summary>
    public class BackgroundSettings
    {
        /// <summary>
        /// 背景类型（None, SolidColor, Gradient, Image）
        /// </summary>
        public string BackgroundType { get; set; } = "None";

        /// <summary>
        /// 背景颜色
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 渐变起始颜色
        /// </summary>
        public string GradientStartColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 渐变结束颜色
        /// </summary>
        public string GradientEndColor { get; set; } = "#000000";

        /// <summary>
        /// 渐变方向（Horizontal, Vertical, Diagonal）
        /// </summary>
        public string GradientDirection { get; set; } = "Horizontal";

        /// <summary>
        /// 背景图片路径
        /// </summary>
        public string ImagePath { get; set; } = string.Empty;

        /// <summary>
        /// 图片填充模式（Stretch, Tile, Center, Fit, Fill）
        /// </summary>
        public string ImageFillMode { get; set; } = "Stretch";

        /// <summary>
        /// 图片透明度（0-100）
        /// </summary>
        public int ImageTransparency { get; set; } = 100;

        /// <summary>
        /// 克隆设置
        /// </summary>
        public BackgroundSettings Clone()
        {
            return new BackgroundSettings
            {
                BackgroundType = this.BackgroundType,
                BackgroundColor = this.BackgroundColor,
                GradientStartColor = this.GradientStartColor,
                GradientEndColor = this.GradientEndColor,
                GradientDirection = this.GradientDirection,
                ImagePath = this.ImagePath,
                ImageFillMode = this.ImageFillMode,
                ImageTransparency = this.ImageTransparency
            };
        }
    }

    /// <summary>
    /// 删除范围枚举 - 定义删除操作的作用范围
    /// </summary>
    public enum DeletionScope
    {
        /// <summary>
        /// 普通幻灯片页 - 只处理普通的幻灯片内容页
        /// </summary>
        NormalSlides = 1,

        /// <summary>
        /// 母版 - 只处理幻灯片母版
        /// </summary>
        Masters = 2,

        /// <summary>
        /// 母版版式页 - 只处理母版下的版式页
        /// </summary>
        LayoutSlides = 4,

        /// <summary>
        /// 普通幻灯片页和母版 - 同时处理普通幻灯片和母版
        /// </summary>
        NormalSlidesAndMasters = NormalSlides | Masters,

        /// <summary>
        /// 普通幻灯片页和版式页 - 同时处理普通幻灯片和版式页
        /// </summary>
        NormalSlidesAndLayouts = NormalSlides | LayoutSlides,

        /// <summary>
        /// 母版和版式页 - 同时处理母版和版式页
        /// </summary>
        MastersAndLayouts = Masters | LayoutSlides,

        /// <summary>
        /// 全部 - 处理普通幻灯片页、母版和版式页
        /// </summary>
        All = NormalSlides | Masters | LayoutSlides
    }

    /// <summary>
    /// 内容删除设置配置类
    /// </summary>
    public class ContentDeletionSettings
    {
        /// <summary>
        /// 删除文档设置
        /// </summary>
        public DocumentDeletionSettings DocumentDeletion { get; set; } = new DocumentDeletionSettings();

        /// <summary>
        /// 内容删除设置
        /// </summary>
        public ContentRemovalSettings ContentRemoval { get; set; } = new ContentRemovalSettings();

        /// <summary>
        /// 文本删除设置
        /// </summary>
        public TextDeletionSettings TextDeletion { get; set; } = new TextDeletionSettings();

        /// <summary>
        /// 图片删除设置
        /// </summary>
        public ImageDeletionSettings ImageDeletion { get; set; } = new ImageDeletionSettings();

        /// <summary>
        /// 表格删除设置
        /// </summary>
        public TableDeletionSettings TableDeletion { get; set; } = new TableDeletionSettings();

        /// <summary>
        /// 图表删除设置
        /// </summary>
        public ChartDeletionSettings ChartDeletion { get; set; } = new ChartDeletionSettings();

        /// <summary>
        /// 音频视频删除设置
        /// </summary>
        public MediaDeletionSettings MediaDeletion { get; set; } = new MediaDeletionSettings();

        /// <summary>
        /// 联系方式删除设置
        /// </summary>
        public ContactDeletionSettings ContactDeletion { get; set; } = new ContactDeletionSettings();

        /// <summary>
        /// 动画删除设置
        /// </summary>
        public AnimationDeletionSettings AnimationDeletion { get; set; } = new AnimationDeletionSettings();

        /// <summary>
        /// 备注删除设置
        /// </summary>
        public NotesDeletionSettings NotesDeletion { get; set; } = new NotesDeletionSettings();

        /// <summary>
        /// 格式删除设置
        /// </summary>
        public FormatDeletionSettings FormatDeletion { get; set; } = new FormatDeletionSettings();
    }

    /// <summary>
    /// 删除文档设置
    /// </summary>
    public class DocumentDeletionSettings
    {
        /// <summary>
        /// 是否启用文档删除功能（总开关）
        /// </summary>
        public bool EnableDocumentDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否启用文件名长度检查
        /// </summary>
        public bool EnableFileNameLengthCheck { get; set; } = false;

        /// <summary>
        /// 文件名最小长度
        /// </summary>
        public int FileNameMinLength { get; set; } = 1;

        /// <summary>
        /// 文件名最大长度
        /// </summary>
        public int FileNameMaxLength { get; set; } = 100;

        /// <summary>
        /// 是否启用文件大小检查
        /// </summary>
        public bool EnableFileSizeCheck { get; set; } = false;

        /// <summary>
        /// 文件最小大小
        /// </summary>
        public long FileMinSize { get; set; } = 1;

        /// <summary>
        /// 文件最小大小单位（B, KB, MB, GB）
        /// </summary>
        public string FileMinSizeUnit { get; set; } = "KB";

        /// <summary>
        /// 文件最大大小
        /// </summary>
        public long FileMaxSize { get; set; } = 10240; // 10MB

        /// <summary>
        /// 文件最大大小单位（B, KB, MB, GB）
        /// </summary>
        public string FileMaxSizeUnit { get; set; } = "KB";

        /// <summary>
        /// 文件大小单位（B, KB, MB）- 保留用于向后兼容
        /// </summary>
        [Obsolete("请使用 FileMinSizeUnit 和 FileMaxSizeUnit 分别设置最小和最大大小的单位")]
        public string FileSizeUnit { get; set; } = "KB";

        /// <summary>
        /// 是否启用内容字符数检查
        /// </summary>
        public bool EnableContentCharCountCheck { get; set; } = false;

        /// <summary>
        /// 内容最小字符数
        /// </summary>
        public int ContentMinCharCount { get; set; } = 1;

        /// <summary>
        /// 内容最大字符数
        /// </summary>
        public int ContentMaxCharCount { get; set; } = 100000;

        /// <summary>
        /// 是否启用页数检查
        /// </summary>
        public bool EnablePageCountCheck { get; set; } = false;

        /// <summary>
        /// 最小页数
        /// </summary>
        public int MinPageCount { get; set; } = 2;

        /// <summary>
        /// 最大页数
        /// </summary>
        public int MaxPageCount { get; set; } = 200;

        /// <summary>
        /// 是否启用文件名非法词检查
        /// </summary>
        public bool EnableFileNameIllegalWordsCheck { get; set; } = false;

        /// <summary>
        /// 文件名非法词列表
        /// </summary>
        public List<string> FileNameIllegalWords { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用内容非法词检查
        /// </summary>
        public bool EnableContentIllegalWordsCheck { get; set; } = false;

        /// <summary>
        /// 内容非法词列表
        /// </summary>
        public List<string> ContentIllegalWords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 内容删除设置
    /// </summary>
    public class ContentRemovalSettings
    {
        /// <summary>
        /// 是否启用内容删除功能
        /// </summary>
        public bool EnableContentRemoval { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除空白幻灯片
        /// </summary>
        public bool DeleteBlankSlides { get; set; } = false;

        /// <summary>
        /// 是否删除不包含任何文字内容的页 - 删除只含图片或图形但没有文字的幻灯片
        /// </summary>
        public bool DeleteSlidesWithoutText { get; set; } = false;

        /// <summary>
        /// 是否删除第一页
        /// </summary>
        public bool DeleteFirstSlide { get; set; } = false;

        /// <summary>
        /// 是否删除最后一页
        /// </summary>
        public bool DeleteLastSlide { get; set; } = false;

        /// <summary>
        /// 是否启用指定页范围删除
        /// </summary>
        public bool EnableSlideRangeDeletion { get; set; } = false;

        /// <summary>
        /// 删除页范围开始页
        /// </summary>
        public int SlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 删除页范围结束页
        /// </summary>
        public int SlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否启用包含关键词的页删除
        /// </summary>
        public bool EnableKeywordSlidesDeletion { get; set; } = false;

        /// <summary>
        /// 页删除关键词列表
        /// </summary>
        public List<string> SlideKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用指定图片幻灯片删除 - 删除包含指定图片的幻灯片
        /// </summary>
        public bool EnableSpecificImageSlidesDeletion { get; set; } = false;

        /// <summary>
        /// 指定图片名称列表 - 包含这些图片的幻灯片将被删除
        /// </summary>
        public List<string> SpecificImageNames { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除空白段落
        /// </summary>
        public bool DeleteBlankParagraphs { get; set; } = false;

        /// <summary>
        /// 是否删除空白行
        /// </summary>
        public bool DeleteBlankLines { get; set; } = false;
    }

    /// <summary>
    /// 文本删除设置
    /// </summary>
    public class TextDeletionSettings
    {
        /// <summary>
        /// 是否启用文本删除功能（总开关）
        /// </summary>
        public bool EnableTextDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有文本 - 删除PPT中的所有文本内容
        /// </summary>
        public bool DeleteAllText { get; set; } = false;

        /// <summary>
        /// 是否删除文本框 - 删除PPT中的文本框
        /// </summary>
        public bool DeleteTextBoxes { get; set; } = false;

        /// <summary>
        /// 是否删除标题 - 删除PPT中的标题文本
        /// </summary>
        public bool DeleteTitles { get; set; } = false;

        /// <summary>
        /// 是否删除项目符号 - 删除PPT中的项目符号和编号列表
        /// </summary>
        public bool DeleteBulletPoints { get; set; } = false;

        /// <summary>
        /// 是否删除特定文本 - 删除指定的文本内容
        /// </summary>
        public bool DeleteSpecificText { get; set; } = false;

        /// <summary>
        /// 特定文本内容列表 - 要删除的具体文本内容
        /// </summary>
        public List<string> SpecificTextContent { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除文本范围 - 删除指定幻灯片范围内的文本
        /// </summary>
        public bool DeleteTextRange { get; set; } = false;

        /// <summary>
        /// 文本删除范围起始页 - 文本删除的起始页码
        /// </summary>
        public int TextSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 文本删除范围结束页 - 文本删除的结束页码
        /// </summary>
        public int TextSlideRangeEnd { get; set; } = 10;

        /// <summary>
        /// 是否删除包含指定文本的段落
        /// </summary>
        public bool DeleteParagraphsWithText { get; set; } = false;

        /// <summary>
        /// 段落删除关键词列表
        /// </summary>
        public List<string> ParagraphKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除包含指定文本的文本框
        /// </summary>
        public bool DeleteTextBoxesWithText { get; set; } = false;

        /// <summary>
        /// 文本框删除关键词列表
        /// </summary>
        public List<string> TextBoxKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否删除包含指定文本的表格
        /// </summary>
        public bool DeleteTablesWithText { get; set; } = false;

        /// <summary>
        /// 表格删除关键词列表
        /// </summary>
        public List<string> TableKeywords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 位置区域定义类
    /// </summary>
    public class PositionRegion
    {
        /// <summary>
        /// 区域名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 位置区域左边距百分比（0-100）
        /// </summary>
        public float XPercent { get; set; } = 0;

        /// <summary>
        /// 位置区域上边距百分比（0-100）
        /// </summary>
        public float YPercent { get; set; } = 0;

        /// <summary>
        /// 位置区域宽度百分比（0-100）
        /// </summary>
        public float WidthPercent { get; set; } = 20;

        /// <summary>
        /// 位置区域高度百分比（0-100）
        /// </summary>
        public float HeightPercent { get; set; } = 20;

        /// <summary>
        /// 是否启用此区域
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 构造函数
        /// </summary>
        public PositionRegion() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">区域名称</param>
        /// <param name="x">左边距百分比</param>
        /// <param name="y">上边距百分比</param>
        /// <param name="width">宽度百分比</param>
        /// <param name="height">高度百分比</param>
        public PositionRegion(string name, float x, float y, float width, float height)
        {
            Name = name;
            XPercent = x;
            YPercent = y;
            WidthPercent = width;
            HeightPercent = height;
        }

        /// <summary>
        /// 复制构造函数
        /// </summary>
        /// <param name="other">要复制的区域</param>
        public PositionRegion(PositionRegion other)
        {
            Name = other.Name;
            XPercent = other.XPercent;
            YPercent = other.YPercent;
            WidthPercent = other.WidthPercent;
            HeightPercent = other.HeightPercent;
            IsEnabled = other.IsEnabled;
        }

        /// <summary>
        /// 获取区域描述
        /// </summary>
        /// <returns>区域描述字符串</returns>
        public override string ToString()
        {
            return $"{Name} ({XPercent:F1}%, {YPercent:F1}%, {WidthPercent:F1}%×{HeightPercent:F1}%)";
        }
    }

    /// <summary>
    /// 图片删除设置
    /// </summary>
    public class ImageDeletionSettings
    {
        /// <summary>
        /// 是否启用图片删除功能（总开关）
        /// </summary>
        public bool EnableImageDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有图片
        /// </summary>
        public bool DeleteAllImages { get; set; } = false;

        /// <summary>
        /// 是否启用指定图片删除
        /// </summary>
        public bool EnableSpecificImageDeletion { get; set; } = false;

        /// <summary>
        /// 指定删除的图片名称列表
        /// </summary>
        public List<string> SpecificImageNames { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用页范围图片删除
        /// </summary>
        public bool EnableSlideRangeImageDeletion { get; set; } = false;

        /// <summary>
        /// 图片删除页范围开始页
        /// </summary>
        public int ImageSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 图片删除页范围结束页
        /// </summary>
        public int ImageSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页图片
        /// </summary>
        public bool DeleteLastSlideImages { get; set; } = false;

        /// <summary>
        /// 是否启用位置条件图片删除
        /// </summary>
        public bool EnablePositionImageDeletion { get; set; } = false;

        /// <summary>
        /// 位置删除区域列表
        /// </summary>
        public List<PositionRegion> PositionRegions { get; set; } = new List<PositionRegion>();

        /// <summary>
        /// 位置区域左边距百分比（0-100）- 保持向后兼容性
        /// </summary>
        public float PositionXPercent { get; set; } = 0;

        /// <summary>
        /// 位置区域上边距百分比（0-100）- 保持向后兼容性
        /// </summary>
        public float PositionYPercent { get; set; } = 0;

        /// <summary>
        /// 位置区域宽度百分比（0-100）- 保持向后兼容性
        /// </summary>
        public float PositionWidthPercent { get; set; } = 20;

        /// <summary>
        /// 位置区域高度百分比（0-100）- 保持向后兼容性
        /// </summary>
        public float PositionHeightPercent { get; set; } = 20;

        /// <summary>
        /// 是否删除背景图片
        /// </summary>
        public bool DeleteBackgroundImages { get; set; } = false;
    }

    /// <summary>
    /// 表格删除设置
    /// </summary>
    public class TableDeletionSettings
    {
        /// <summary>
        /// 是否启用表格删除功能（总开关）
        /// </summary>
        public bool EnableTableDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有表格
        /// </summary>
        public bool DeleteAllTables { get; set; } = false;

        /// <summary>
        /// 是否启用页范围表格删除
        /// </summary>
        public bool EnableSlideRangeTableDeletion { get; set; } = false;

        /// <summary>
        /// 表格删除页范围开始页
        /// </summary>
        public int TableSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 表格删除页范围结束页
        /// </summary>
        public int TableSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页表格
        /// </summary>
        public bool DeleteLastSlideTables { get; set; } = false;
    }

    /// <summary>
    /// 图表删除设置
    /// </summary>
    public class ChartDeletionSettings
    {
        /// <summary>
        /// 是否启用图表删除功能（总开关）
        /// </summary>
        public bool EnableChartDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有图表
        /// </summary>
        public bool DeleteAllCharts { get; set; } = false;

        /// <summary>
        /// 是否启用页范围图表删除
        /// </summary>
        public bool EnableSlideRangeChartDeletion { get; set; } = false;

        /// <summary>
        /// 图表删除页范围开始页
        /// </summary>
        public int ChartSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 图表删除页范围结束页
        /// </summary>
        public int ChartSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页图表
        /// </summary>
        public bool DeleteLastSlideCharts { get; set; } = false;

        /// <summary>
        /// 是否删除包含指定文本的图表
        /// </summary>
        public bool DeleteChartsWithText { get; set; } = false;

        /// <summary>
        /// 图表文本关键词列表 - 包含这些关键词的图表将被删除
        /// </summary>
        public List<string> ChartKeywords { get; set; } = new List<string>();
    }

    /// <summary>
    /// 音频视频删除设置
    /// </summary>
    public class MediaDeletionSettings
    {
        /// <summary>
        /// 是否启用媒体删除功能（总开关）
        /// </summary>
        public bool EnableMediaDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有音频
        /// </summary>
        public bool DeleteAllAudio { get; set; } = false;

        /// <summary>
        /// 是否启用页范围音频删除
        /// </summary>
        public bool EnableSlideRangeAudioDeletion { get; set; } = false;

        /// <summary>
        /// 音频删除页范围开始页
        /// </summary>
        public int AudioSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 音频删除页范围结束页
        /// </summary>
        public int AudioSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页音频
        /// </summary>
        public bool DeleteLastSlideAudio { get; set; } = false;

        /// <summary>
        /// 是否删除所有视频
        /// </summary>
        public bool DeleteAllVideo { get; set; } = false;

        /// <summary>
        /// 是否启用页范围视频删除
        /// </summary>
        public bool EnableSlideRangeVideoDeletion { get; set; } = false;

        /// <summary>
        /// 视频删除页范围开始页
        /// </summary>
        public int VideoSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 视频删除页范围结束页
        /// </summary>
        public int VideoSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页视频
        /// </summary>
        public bool DeleteLastSlideVideo { get; set; } = false;
    }

    /// <summary>
    /// 联系方式删除设置
    /// </summary>
    public class ContactDeletionSettings
    {
        /// <summary>
        /// 是否启用联系方式删除功能（总开关）
        /// </summary>
        public bool EnableContactDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除手机号码
        /// </summary>
        public bool DeletePhoneNumbers { get; set; } = false;

        /// <summary>
        /// 是否删除固定电话号码
        /// </summary>
        public bool DeleteLandlineNumbers { get; set; } = false;

        /// <summary>
        /// 是否删除电子邮箱
        /// </summary>
        public bool DeleteEmailAddresses { get; set; } = false;

        /// <summary>
        /// 是否删除网址
        /// </summary>
        public bool DeleteWebsites { get; set; } = false;

        /// <summary>
        /// 是否删除超链接
        /// </summary>
        public bool DeleteHyperlinks { get; set; } = false;
    }

    /// <summary>
    /// 动画删除设置
    /// </summary>
    public class AnimationDeletionSettings
    {
        /// <summary>
        /// 是否启用动画删除功能（总开关）
        /// </summary>
        public bool EnableAnimationDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除所有动画效果
        /// </summary>
        public bool DeleteAllAnimations { get; set; } = false;

        /// <summary>
        /// 是否启用页范围动画删除
        /// </summary>
        public bool EnableSlideRangeAnimationDeletion { get; set; } = false;

        /// <summary>
        /// 动画删除页范围开始页
        /// </summary>
        public int AnimationSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 动画删除页范围结束页
        /// </summary>
        public int AnimationSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页动画
        /// </summary>
        public bool DeleteLastSlideAnimations { get; set; } = false;

        /// <summary>
        /// 是否删除所有切换效果
        /// </summary>
        public bool DeleteAllTransitions { get; set; } = false;

        /// <summary>
        /// 是否启用页范围切换效果删除
        /// </summary>
        public bool EnableSlideRangeTransitionDeletion { get; set; } = false;

        /// <summary>
        /// 切换效果删除页范围开始页
        /// </summary>
        public int TransitionSlideRangeStart { get; set; } = 1;

        /// <summary>
        /// 切换效果删除页范围结束页
        /// </summary>
        public int TransitionSlideRangeEnd { get; set; } = 1;

        /// <summary>
        /// 是否删除最后一页切换效果
        /// </summary>
        public bool DeleteLastSlideTransitions { get; set; } = false;
    }

    /// <summary>
    /// 备注删除设置
    /// </summary>
    public class NotesDeletionSettings
    {
        /// <summary>
        /// 是否启用备注删除功能（总开关）
        /// </summary>
        public bool EnableNotesDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除幻灯片备注
        /// </summary>
        public bool DeleteSlideNotes { get; set; } = false;

        /// <summary>
        /// 是否清空备注内容
        /// </summary>
        public bool ClearNotesContent { get; set; } = false;
    }

    /// <summary>
    /// 格式删除设置
    /// </summary>
    public class FormatDeletionSettings
    {
        /// <summary>
        /// 是否启用格式删除功能（总开关）
        /// </summary>
        public bool EnableFormatDeletion { get; set; } = false;

        /// <summary>
        /// 删除范围 - 指定删除操作的作用范围（普通幻灯片页、母版、版式页）
        /// </summary>
        public DeletionScope DeletionScope { get; set; } = DeletionScope.NormalSlides;

        /// <summary>
        /// 是否删除字体格式
        /// </summary>
        public bool DeleteFontFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除段落格式
        /// </summary>
        public bool DeleteParagraphFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除表格格式
        /// </summary>
        public bool DeleteTableFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除列表格式
        /// </summary>
        public bool DeleteListFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除形状格式
        /// </summary>
        public bool DeleteShapeFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除图片格式
        /// </summary>
        public bool DeleteImageFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除图表格式
        /// </summary>
        public bool DeleteChartFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除背景格式
        /// </summary>
        public bool DeleteBackgroundFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除主题格式
        /// </summary>
        public bool DeleteThemeFormatting { get; set; } = false;

        /// <summary>
        /// 是否删除动画格式
        /// </summary>
        public bool DeleteAnimationFormatting { get; set; } = false;
    }

    #region 内容替换设置相关类

    /// <summary>
    /// 内容替换设置配置类
    /// </summary>
    public class ContentReplacementSettings
    {
        /// <summary>
        /// 文本替换设置
        /// </summary>
        public TextReplacementSettings TextReplacement { get; set; } = new TextReplacementSettings();

        /// <summary>
        /// 形状替换设置
        /// </summary>
        public ShapeReplacementSettings ShapeReplacement { get; set; } = new ShapeReplacementSettings();

        /// <summary>
        /// 字体替换设置
        /// </summary>
        public FontReplacementSettings FontReplacement { get; set; } = new FontReplacementSettings();

        /// <summary>
        /// 颜色替换设置
        /// </summary>
        public ColorReplacementSettings ColorReplacement { get; set; } = new ColorReplacementSettings();
    }

    /// <summary>
    /// 替换范围设置类 - 定义替换操作应用的幻灯片范围
    /// </summary>
    public class ReplacementScope
    {
        /// <summary>
        /// 是否在普通幻灯片页中进行替换
        /// </summary>
        public bool IncludeNormalSlides { get; set; } = true;

        /// <summary>
        /// 是否在母版页中进行替换
        /// </summary>
        public bool IncludeMasterSlides { get; set; } = true;

        /// <summary>
        /// 是否在母版版式页中进行替换
        /// </summary>
        public bool IncludeLayoutSlides { get; set; } = true;
    }

    /// <summary>
    /// 文本替换设置
    /// </summary>
    public class TextReplacementSettings
    {
        /// <summary>
        /// 是否启用文本替换功能
        /// </summary>
        public bool EnableTextReplacement { get; set; } = false;

        /// <summary>
        /// 文本替换规则列表
        /// </summary>
        public List<TextReplacementRule> ReplacementRules { get; set; } = new List<TextReplacementRule>();

        /// <summary>
        /// 是否启用普通文本替换
        /// </summary>
        public bool EnableNormalTextReplacement { get; set; } = true;

        /// <summary>
        /// 是否启用正则表达式替换
        /// </summary>
        public bool EnableRegexReplacement { get; set; } = false;

        /// <summary>
        /// 是否启用批量文本替换
        /// </summary>
        public bool EnableBatchTextReplacement { get; set; } = false;

        /// <summary>
        /// 是否启用指定范围替换
        /// </summary>
        public bool EnableRangeReplacement { get; set; } = false;

        /// <summary>
        /// 替换范围设置 - 定义文本替换应用的幻灯片范围
        /// </summary>
        public ReplacementScope ReplacementScope { get; set; } = new ReplacementScope();

        /// <summary>
        /// 替换范围设置
        /// </summary>
        public TextReplacementRange ReplacementRange { get; set; } = new TextReplacementRange();
    }

    /// <summary>
    /// 文本替换规则
    /// </summary>
    public class TextReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 查找文本
        /// </summary>
        public string FindText { get; set; } = "";

        /// <summary>
        /// 替换文本
        /// </summary>
        public string ReplaceText { get; set; } = "";

        /// <summary>
        /// 是否使用正则表达式
        /// </summary>
        public bool UseRegex { get; set; } = false;

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 是否全词匹配
        /// </summary>
        public bool WholeWord { get; set; } = false;

        /// <summary>
        /// 替换范围类型
        /// </summary>
        public TextReplacementRangeType RangeType { get; set; } = TextReplacementRangeType.All;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 文本替换范围设置
    /// </summary>
    public class TextReplacementRange
    {
        /// <summary>
        /// 是否替换标题文本
        /// </summary>
        public bool ReplaceInTitles { get; set; } = true;

        /// <summary>
        /// 是否替换内容文本
        /// </summary>
        public bool ReplaceInContent { get; set; } = true;

        /// <summary>
        /// 是否替换备注文本
        /// </summary>
        public bool ReplaceInNotes { get; set; } = false;

        /// <summary>
        /// 是否替换表格文本
        /// </summary>
        public bool ReplaceInTables { get; set; } = true;

        /// <summary>
        /// 是否替换图表文本
        /// </summary>
        public bool ReplaceInCharts { get; set; } = false;
    }

    /// <summary>
    /// 文本替换范围类型
    /// </summary>
    public enum TextReplacementRangeType
    {
        /// <summary>
        /// 全部
        /// </summary>
        All,
        /// <summary>
        /// 仅标题
        /// </summary>
        TitlesOnly,
        /// <summary>
        /// 仅内容
        /// </summary>
        ContentOnly,
        /// <summary>
        /// 仅备注
        /// </summary>
        NotesOnly,
        /// <summary>
        /// 自定义范围
        /// </summary>
        Custom
    }

    /// <summary>
    /// 形状替换设置
    /// </summary>
    public class ShapeReplacementSettings
    {
        /// <summary>
        /// 是否启用形状替换功能
        /// </summary>
        public bool EnableShapeReplacement { get; set; } = false;

        /// <summary>
        /// 是否启用图片替换
        /// </summary>
        public bool EnableImageReplacement { get; set; } = false;

        /// <summary>
        /// 图片替换规则列表
        /// </summary>
        public List<ImageReplacementRule> ImageReplacementRules { get; set; } = new List<ImageReplacementRule>();

        /// <summary>
        /// 是否启用文本框替换
        /// </summary>
        public bool EnableTextBoxReplacement { get; set; } = false;

        /// <summary>
        /// 文本框替换规则列表
        /// </summary>
        public List<TextBoxReplacementRule> TextBoxReplacementRules { get; set; } = new List<TextBoxReplacementRule>();

        /// <summary>
        /// 是否启用形状样式替换
        /// </summary>
        public bool EnableShapeStyleReplacement { get; set; } = false;

        /// <summary>
        /// 形状样式替换规则列表
        /// </summary>
        public List<ShapeStyleReplacementRule> ShapeStyleReplacementRules { get; set; } = new List<ShapeStyleReplacementRule>();

        /// <summary>
        /// 替换范围设置 - 定义形状替换应用的幻灯片范围
        /// </summary>
        public ReplacementScope ReplacementScope { get; set; } = new ReplacementScope();
    }

    /// <summary>
    /// 图片替换规则
    /// </summary>
    public class ImageReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源图片名称或标识符
        /// </summary>
        public string SourceImageName { get; set; } = "";

        /// <summary>
        /// 源图片文件路径
        /// </summary>
        public string SourceImagePath { get; set; } = "";

        /// <summary>
        /// 新图片文件路径
        /// </summary>
        public string NewImagePath { get; set; } = "";

        /// <summary>
        /// 目标图片文件路径
        /// </summary>
        public string TargetImagePath { get; set; } = "";

        /// <summary>
        /// 匹配方式
        /// </summary>
        public ImageMatchType MatchType { get; set; } = ImageMatchType.ByName;

        /// <summary>
        /// 是否按文件名匹配
        /// </summary>
        public bool MatchByFileName { get; set; } = true;

        /// <summary>
        /// 是否按图片内容匹配
        /// </summary>
        public bool MatchByContent { get; set; } = false;

        /// <summary>
        /// 是否按图片尺寸匹配
        /// </summary>
        public bool MatchBySize { get; set; } = false;

        /// <summary>
        /// 是否按位置匹配
        /// </summary>
        public bool MatchByPosition { get; set; } = false;

        /// <summary>
        /// 是否替换所有匹配的图片
        /// </summary>
        public bool ReplaceAllInstances { get; set; } = true;

        /// <summary>
        /// 是否保持原图片尺寸
        /// </summary>
        public bool KeepOriginalSize { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 图片匹配类型
    /// </summary>
    public enum ImageMatchType
    {
        /// <summary>
        /// 按图片名称匹配
        /// </summary>
        ByName,
        /// <summary>
        /// 按图片索引匹配
        /// </summary>
        ByIndex,
        /// <summary>
        /// 替换所有图片
        /// </summary>
        All
    }

    /// <summary>
    /// 文本框替换规则
    /// </summary>
    public class TextBoxReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源文本框内容
        /// </summary>
        public string SourceTextContent { get; set; } = "";

        /// <summary>
        /// 目标文本框内容
        /// </summary>
        public string TargetTextContent { get; set; } = "";

        /// <summary>
        /// 是否按文本内容匹配
        /// </summary>
        public bool MatchByContent { get; set; } = true;

        /// <summary>
        /// 是否按文本框位置匹配
        /// </summary>
        public bool MatchByPosition { get; set; } = false;

        /// <summary>
        /// 匹配的X坐标
        /// </summary>
        public float MatchX { get; set; } = 0;

        /// <summary>
        /// 匹配的Y坐标
        /// </summary>
        public float MatchY { get; set; } = 0;

        /// <summary>
        /// 匹配的宽度
        /// </summary>
        public float MatchWidth { get; set; } = 0;

        /// <summary>
        /// 匹配的高度
        /// </summary>
        public float MatchHeight { get; set; } = 0;

        /// <summary>
        /// 位置匹配容差
        /// </summary>
        public float PositionTolerance { get; set; } = 10;

        /// <summary>
        /// 是否保持原文本框格式
        /// </summary>
        public bool KeepOriginalFormat { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 形状样式替换规则
    /// </summary>
    public class ShapeStyleReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源形状类型
        /// </summary>
        public string SourceShapeType { get; set; } = "";

        /// <summary>
        /// 目标形状类型
        /// </summary>
        public string TargetShapeType { get; set; } = "";

        /// <summary>
        /// 是否替换填充颜色
        /// </summary>
        public bool ReplaceFillColor { get; set; } = false;

        /// <summary>
        /// 源填充颜色
        /// </summary>
        public string SourceFillColor { get; set; } = "";

        /// <summary>
        /// 目标填充颜色
        /// </summary>
        public string TargetFillColor { get; set; } = "";

        /// <summary>
        /// 是否替换边框颜色
        /// </summary>
        public bool ReplaceBorderColor { get; set; } = false;

        /// <summary>
        /// 源边框颜色
        /// </summary>
        public string SourceBorderColor { get; set; } = "";

        /// <summary>
        /// 目标边框颜色
        /// </summary>
        public string TargetBorderColor { get; set; } = "";

        /// <summary>
        /// 是否替换边框宽度
        /// </summary>
        public bool ReplaceBorderWidth { get; set; } = false;

        /// <summary>
        /// 源边框宽度
        /// </summary>
        public float SourceBorderWidth { get; set; } = 1.0f;

        /// <summary>
        /// 目标边框宽度
        /// </summary>
        public float TargetBorderWidth { get; set; } = 1.0f;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 字体替换设置
    /// </summary>
    public class FontReplacementSettings
    {
        /// <summary>
        /// 是否启用字体替换功能
        /// </summary>
        public bool EnableFontReplacement { get; set; } = false;

        /// <summary>
        /// 是否启用字体名称替换
        /// </summary>
        public bool EnableFontNameReplacement { get; set; } = false;

        /// <summary>
        /// 字体名称替换规则列表
        /// </summary>
        public List<FontNameReplacementRule> FontNameReplacementRules { get; set; } = new List<FontNameReplacementRule>();

        /// <summary>
        /// 是否启用字体样式替换
        /// </summary>
        public bool EnableFontStyleReplacement { get; set; } = false;

        /// <summary>
        /// 字体样式替换规则列表
        /// </summary>
        public List<FontStyleReplacementRule> FontStyleReplacementRules { get; set; } = new List<FontStyleReplacementRule>();

        /// <summary>
        /// 是否启用字体嵌入
        /// </summary>
        public bool EnableFontEmbedding { get; set; } = false;

        /// <summary>
        /// 字体嵌入设置
        /// </summary>
        public FontEmbeddingSettings FontEmbeddingSettings { get; set; } = new FontEmbeddingSettings();

        /// <summary>
        /// 替换范围设置 - 定义字体替换应用的幻灯片范围
        /// </summary>
        public ReplacementScope ReplacementScope { get; set; } = new ReplacementScope();
    }

    /// <summary>
    /// 字体名称替换规则
    /// </summary>
    public class FontNameReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源字体名称
        /// </summary>
        public string SourceFontName { get; set; } = "";

        /// <summary>
        /// 目标字体名称
        /// </summary>
        public string TargetFontName { get; set; } = "";

        /// <summary>
        /// 是否精确匹配字体名称
        /// </summary>
        public bool ExactMatch { get; set; } = true;

        /// <summary>
        /// 是否包含子字体
        /// </summary>
        public bool IncludeSubFonts { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 字体样式替换规则
    /// </summary>
    public class FontStyleReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源字体名称
        /// </summary>
        public string SourceFontName { get; set; } = "";

        /// <summary>
        /// 目标字体名称
        /// </summary>
        public string TargetFontName { get; set; } = "";

        /// <summary>
        /// 是否替换字体大小
        /// </summary>
        public bool ReplaceFontSize { get; set; } = false;

        /// <summary>
        /// 源字体大小
        /// </summary>
        public float SourceFontSize { get; set; } = 12;

        /// <summary>
        /// 目标字体大小
        /// </summary>
        public float TargetFontSize { get; set; } = 12;

        /// <summary>
        /// 是否替换粗体样式
        /// </summary>
        public bool ReplaceBoldStyle { get; set; } = false;

        /// <summary>
        /// 源粗体样式
        /// </summary>
        public bool SourceBoldStyle { get; set; } = false;

        /// <summary>
        /// 目标粗体样式
        /// </summary>
        public bool TargetBoldStyle { get; set; } = false;

        /// <summary>
        /// 是否替换斜体样式
        /// </summary>
        public bool ReplaceItalicStyle { get; set; } = false;

        /// <summary>
        /// 源斜体样式
        /// </summary>
        public bool SourceItalicStyle { get; set; } = false;

        /// <summary>
        /// 目标斜体样式
        /// </summary>
        public bool TargetItalicStyle { get; set; } = false;

        /// <summary>
        /// 是否替换下划线样式
        /// </summary>
        public bool ReplaceUnderlineStyle { get; set; } = false;

        /// <summary>
        /// 源下划线样式
        /// </summary>
        public bool SourceUnderlineStyle { get; set; } = false;

        /// <summary>
        /// 目标下划线样式
        /// </summary>
        public bool TargetUnderlineStyle { get; set; } = false;

        /// <summary>
        /// 源字体样式
        /// </summary>
        public string SourceFontStyle { get; set; } = "";

        /// <summary>
        /// 目标字体样式
        /// </summary>
        public string TargetFontStyle { get; set; } = "";

        /// <summary>
        /// 替换内容描述
        /// </summary>
        public string ReplaceContent { get; set; } = "";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 字体嵌入设置
    /// </summary>
    public class FontEmbeddingSettings
    {
        /// <summary>
        /// 是否嵌入所有字体
        /// </summary>
        public bool EmbedAllFonts { get; set; } = false;

        /// <summary>
        /// 字体嵌入字符范围
        /// </summary>
        public FontEmbedCharacters EmbedCharacters { get; set; } = FontEmbedCharacters.OnlyUsed;

        /// <summary>
        /// 需要嵌入的字体列表
        /// </summary>
        public List<string> FontsToEmbed { get; set; } = new List<string>();

        /// <summary>
        /// 排除嵌入的字体列表
        /// </summary>
        public List<string> FontsToExclude { get; set; } = new List<string>();
    }

    /// <summary>
    /// 字体嵌入字符范围（符合Aspose.Slides API）
    /// </summary>
    public enum FontEmbedCharacters
    {
        /// <summary>
        /// 嵌入所有字符
        /// </summary>
        All,
        /// <summary>
        /// 仅嵌入使用的字符
        /// </summary>
        OnlyUsed
    }

    /// <summary>
    /// 颜色替换设置
    /// </summary>
    public class ColorReplacementSettings
    {
        /// <summary>
        /// 是否启用颜色替换功能
        /// </summary>
        public bool EnableColorReplacement { get; set; } = false;

        /// <summary>
        /// 是否启用主题颜色替换
        /// </summary>
        public bool EnableThemeColorReplacement { get; set; } = false;

        /// <summary>
        /// 主题颜色替换规则列表
        /// </summary>
        public List<ThemeColorReplacementRule> ThemeColorReplacementRules { get; set; } = new List<ThemeColorReplacementRule>();

        /// <summary>
        /// 是否启用自定义颜色替换
        /// </summary>
        public bool EnableCustomColorReplacement { get; set; } = false;

        /// <summary>
        /// 自定义颜色替换规则列表
        /// </summary>
        public List<CustomColorReplacementRule> CustomColorReplacementRules { get; set; } = new List<CustomColorReplacementRule>();

        /// <summary>
        /// 替换范围设置 - 定义颜色替换应用的幻灯片范围
        /// </summary>
        public ReplacementScope ReplacementScope { get; set; } = new ReplacementScope();
    }

    /// <summary>
    /// 主题颜色替换规则
    /// </summary>
    public class ThemeColorReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源主题颜色类型（符合Aspose.Slides SchemeColor）
        /// </summary>
        public SchemeColorType SourceSchemeColor { get; set; } = SchemeColorType.Accent1;

        /// <summary>
        /// 目标主题颜色类型（符合Aspose.Slides SchemeColor）
        /// </summary>
        public SchemeColorType TargetSchemeColor { get; set; } = SchemeColorType.Accent1;

        /// <summary>
        /// 是否应用到文本颜色
        /// </summary>
        public bool ApplyToTextColor { get; set; } = true;

        /// <summary>
        /// 是否应用到填充颜色
        /// </summary>
        public bool ApplyToFillColor { get; set; } = true;

        /// <summary>
        /// 是否应用到边框颜色
        /// </summary>
        public bool ApplyToBorderColor { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 自定义颜色替换规则
    /// </summary>
    public class CustomColorReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 源颜色（RGB格式）
        /// </summary>
        public string SourceColor { get; set; } = "#000000";

        /// <summary>
        /// 目标颜色（RGB格式）
        /// </summary>
        public string TargetColor { get; set; } = "#000000";

        /// <summary>
        /// 是否应用到文本颜色
        /// </summary>
        public bool ApplyToTextColor { get; set; } = true;

        /// <summary>
        /// 是否应用到填充颜色
        /// </summary>
        public bool ApplyToFillColor { get; set; } = true;

        /// <summary>
        /// 是否应用到边框颜色
        /// </summary>
        public bool ApplyToBorderColor { get; set; } = true;

        /// <summary>
        /// 是否应用到背景颜色
        /// </summary>
        public bool ApplyToBackgroundColor { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 主题颜色类型（符合Aspose.Slides SchemeColor枚举）
    /// </summary>
    public enum SchemeColorType
    {
        /// <summary>
        /// 背景1
        /// </summary>
        Background1,
        /// <summary>
        /// 文本1
        /// </summary>
        Text1,
        /// <summary>
        /// 背景2
        /// </summary>
        Background2,
        /// <summary>
        /// 文本2
        /// </summary>
        Text2,
        /// <summary>
        /// 强调色1
        /// </summary>
        Accent1,
        /// <summary>
        /// 强调色2
        /// </summary>
        Accent2,
        /// <summary>
        /// 强调色3
        /// </summary>
        Accent3,
        /// <summary>
        /// 强调色4
        /// </summary>
        Accent4,
        /// <summary>
        /// 强调色5
        /// </summary>
        Accent5,
        /// <summary>
        /// 强调色6
        /// </summary>
        Accent6,
        /// <summary>
        /// 超链接
        /// </summary>
        Hyperlink,
        /// <summary>
        /// 已访问的超链接
        /// </summary>
        FollowedHyperlink
    }

    #endregion

    #region 匹配段落格式设置相关类

    /// <summary>
    /// 匹配段落格式设置配置类
    /// </summary>
    public class ParagraphFormatMatchingSettings
    {
        /// <summary>
        /// 是否启用匹配段落格式功能
        /// </summary>
        public bool EnableParagraphFormatMatching { get; set; } = false;

        /// <summary>
        /// 段落格式匹配规则列表
        /// </summary>
        public List<ParagraphFormatMatchingRule> MatchingRules { get; set; } = new List<ParagraphFormatMatchingRule>();
    }

    /// <summary>
    /// 段落格式匹配规则
    /// </summary>
    public class ParagraphFormatMatchingRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 匹配条件设置
        /// </summary>
        public ParagraphMatchingConditions MatchingConditions { get; set; } = new ParagraphMatchingConditions();

        /// <summary>
        /// 段落格式设置
        /// </summary>
        public ParagraphFormatSettings ParagraphFormat { get; set; } = new ParagraphFormatSettings();

        /// <summary>
        /// 字体格式设置
        /// </summary>
        public FontFormatSettings FontFormat { get; set; } = new FontFormatSettings();

        /// <summary>
        /// 替换范围设置 - 定义段落格式匹配应用的幻灯片范围
        /// </summary>
        public ReplacementScope ReplacementScope { get; set; } = new ReplacementScope();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 段落匹配条件
    /// </summary>
    public class ParagraphMatchingConditions
    {
        /// <summary>
        /// 是否启用段落开头匹配
        /// </summary>
        public bool EnableStartsWith { get; set; } = false;

        /// <summary>
        /// 段落开头匹配文本
        /// </summary>
        public string StartsWithText { get; set; } = "";

        /// <summary>
        /// 是否启用段落包含关键词匹配
        /// </summary>
        public bool EnableContains { get; set; } = false;

        /// <summary>
        /// 段落包含关键词列表
        /// </summary>
        public List<string> ContainsKeywords { get; set; } = new List<string>();

        /// <summary>
        /// 是否启用段落结尾匹配
        /// </summary>
        public bool EnableEndsWith { get; set; } = false;

        /// <summary>
        /// 段落结尾匹配文本
        /// </summary>
        public string EndsWithText { get; set; } = "";

        /// <summary>
        /// 是否启用正则表达式匹配
        /// </summary>
        public bool EnableRegex { get; set; } = false;

        /// <summary>
        /// 正则表达式模式
        /// </summary>
        public string RegexPattern { get; set; } = "";

        /// <summary>
        /// 是否启用段落字符数限制
        /// </summary>
        public bool EnableCharacterCountLimit { get; set; } = false;

        /// <summary>
        /// 段落最小字符数
        /// </summary>
        public int MinCharacterCount { get; set; } = 1;

        /// <summary>
        /// 段落最大字符数
        /// </summary>
        public int MaxCharacterCount { get; set; } = 1000;

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 是否全词匹配
        /// </summary>
        public bool WholeWord { get; set; } = false;
    }

    /// <summary>
    /// 段落格式设置
    /// </summary>
    public class ParagraphFormatSettings
    {
        /// <summary>
        /// 是否启用段落格式设置
        /// </summary>
        public bool EnableParagraphFormat { get; set; } = false;

        /// <summary>
        /// 对齐方式
        /// </summary>
        public ParagraphAlignment Alignment { get; set; } = ParagraphAlignment.Left;

        /// <summary>
        /// 是否启用缩进设置
        /// </summary>
        public bool EnableIndentation { get; set; } = false;

        /// <summary>
        /// 文本之前缩进距离（磅）
        /// </summary>
        public float LeftIndent { get; set; } = 0;

        /// <summary>
        /// 左边距（磅）
        /// </summary>
        public float MarginLeft { get; set; } = 0;

        /// <summary>
        /// 右边距（磅）
        /// </summary>
        public float MarginRight { get; set; } = 0;

        /// <summary>
        /// 特殊缩进类型
        /// </summary>
        public SpecialIndentType SpecialIndent { get; set; } = SpecialIndentType.None;

        /// <summary>
        /// 特殊缩进值（字符数）
        /// </summary>
        public float SpecialIndentValue { get; set; } = 0;

        /// <summary>
        /// 是否启用间距设置
        /// </summary>
        public bool EnableSpacing { get; set; } = false;

        /// <summary>
        /// 段前间距（磅）
        /// </summary>
        public float SpaceBefore { get; set; } = 0;

        /// <summary>
        /// 段后间距（磅）
        /// </summary>
        public float SpaceAfter { get; set; } = 0;

        /// <summary>
        /// 行距类型
        /// </summary>
        public LineSpacingType LineSpacingType { get; set; } = LineSpacingType.Single;

        /// <summary>
        /// 行距值（当类型为固定值或多倍行距时使用）
        /// </summary>
        public float LineSpacingValue { get; set; } = 1.0f;

        /// <summary>
        /// 是否启用中文控制选项
        /// </summary>
        public bool EnableChineseControl { get; set; } = false;

        /// <summary>
        /// 按中文习惯控制中文首尾字符
        /// </summary>
        public bool ChineseCharacterControl { get; set; } = false;

        /// <summary>
        /// 允许西文在单词中间换行
        /// </summary>
        public bool AllowLatinWordBreak { get; set; } = false;

        /// <summary>
        /// 允许标点移除边界
        /// </summary>
        public bool AllowPunctuationOverhang { get; set; } = false;

        /// <summary>
        /// 文本对齐方式
        /// </summary>
        public TextVerticalAlignment TextAlignment { get; set; } = TextVerticalAlignment.Auto;

        /// <summary>
        /// 是否启用字体对齐设置
        /// </summary>
        public bool EnableFontAlignment { get; set; } = false;

        /// <summary>
        /// 字体对齐方式
        /// </summary>
        public FontAlignmentType FontAlignment { get; set; } = FontAlignmentType.Automatic;

        /// <summary>
        /// 是否启用从右到左书写
        /// </summary>
        public bool EnableRightToLeft { get; set; } = false;

        /// <summary>
        /// 从右到左书写
        /// </summary>
        public bool RightToLeft { get; set; } = false;
    }

    /// <summary>
    /// 字体格式设置
    /// </summary>
    public class FontFormatSettings
    {
        /// <summary>
        /// 是否启用字体格式设置
        /// </summary>
        public bool EnableFontFormat { get; set; } = false;

        /// <summary>
        /// 是否设置中文字体
        /// </summary>
        public bool SetChineseFont { get; set; } = false;

        /// <summary>
        /// 中文字体名称
        /// </summary>
        public string ChineseFontName { get; set; } = "宋体";

        /// <summary>
        /// 是否设置西文字体
        /// </summary>
        public bool SetLatinFont { get; set; } = false;

        /// <summary>
        /// 西文字体名称
        /// </summary>
        public string LatinFontName { get; set; } = "Arial";

        /// <summary>
        /// 字体样式
        /// </summary>
        public FontStyleType FontStyle { get; set; } = FontStyleType.Regular;

        /// <summary>
        /// 是否设置字体大小
        /// </summary>
        public bool SetFontSize { get; set; } = false;

        /// <summary>
        /// 字体大小
        /// </summary>
        public float FontSize { get; set; } = 12;

        /// <summary>
        /// 是否设置字体颜色
        /// </summary>
        public bool SetFontColor { get; set; } = false;

        /// <summary>
        /// 字体颜色（RGB格式）
        /// </summary>
        public string FontColor { get; set; } = "#000000";

        /// <summary>
        /// 是否设置下划线
        /// </summary>
        public bool SetUnderline { get; set; } = false;

        /// <summary>
        /// 下划线线型
        /// </summary>
        public UnderlineType UnderlineType { get; set; } = UnderlineType.None;

        /// <summary>
        /// 下划线颜色（RGB格式）
        /// </summary>
        public string UnderlineColor { get; set; } = "#000000";

        /// <summary>
        /// 是否设置文字效果
        /// </summary>
        public bool SetTextEffects { get; set; } = false;

        /// <summary>
        /// 是否删除线
        /// </summary>
        public bool Strikethrough { get; set; } = false;

        /// <summary>
        /// 是否双删除线
        /// </summary>
        public bool DoubleStrikethrough { get; set; } = false;

        /// <summary>
        /// 是否上标
        /// </summary>
        public bool Superscript { get; set; } = false;

        /// <summary>
        /// 是否下标
        /// </summary>
        public bool Subscript { get; set; } = false;
    }

    /// <summary>
    /// 段落对齐方式枚举
    /// </summary>
    public enum ParagraphAlignment
    {
        /// <summary>
        /// 左对齐
        /// </summary>
        Left,
        /// <summary>
        /// 居中
        /// </summary>
        Center,
        /// <summary>
        /// 右对齐
        /// </summary>
        Right,
        /// <summary>
        /// 两端对齐
        /// </summary>
        Justify,
        /// <summary>
        /// 分散对齐
        /// </summary>
        Distribute
    }

    /// <summary>
    /// 特殊缩进类型枚举
    /// </summary>
    public enum SpecialIndentType
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 首行缩进
        /// </summary>
        FirstLine,
        /// <summary>
        /// 悬挂缩进
        /// </summary>
        Hanging
    }

    /// <summary>
    /// 行距类型枚举
    /// </summary>
    public enum LineSpacingType
    {
        /// <summary>
        /// 单倍行距
        /// </summary>
        Single,
        /// <summary>
        /// 1.5倍行距
        /// </summary>
        OneAndHalf,
        /// <summary>
        /// 2倍行距
        /// </summary>
        Double,
        /// <summary>
        /// 多倍行距
        /// </summary>
        Multiple,
        /// <summary>
        /// 固定值
        /// </summary>
        Fixed
    }

    /// <summary>
    /// 文本垂直对齐方式枚举
    /// </summary>
    public enum TextVerticalAlignment
    {
        /// <summary>
        /// 自动
        /// </summary>
        Auto,
        /// <summary>
        /// 居中
        /// </summary>
        Center,
        /// <summary>
        /// 基线
        /// </summary>
        Baseline,
        /// <summary>
        /// 底部
        /// </summary>
        Bottom
    }

    /// <summary>
    /// 字体对齐方式枚举
    /// </summary>
    public enum FontAlignmentType
    {
        /// <summary>
        /// 自动
        /// </summary>
        Automatic,
        /// <summary>
        /// 顶部
        /// </summary>
        Top,
        /// <summary>
        /// 中心
        /// </summary>
        Center,
        /// <summary>
        /// 基线
        /// </summary>
        Baseline,
        /// <summary>
        /// 底部
        /// </summary>
        Bottom
    }

    /// <summary>
    /// 字体样式类型枚举
    /// </summary>
    public enum FontStyleType
    {
        /// <summary>
        /// 常规
        /// </summary>
        Regular,
        /// <summary>
        /// 倾斜
        /// </summary>
        Italic,
        /// <summary>
        /// 加粗
        /// </summary>
        Bold,
        /// <summary>
        /// 倾斜加粗
        /// </summary>
        BoldItalic
    }

    /// <summary>
    /// 下划线类型枚举
    /// </summary>
    public enum UnderlineType
    {
        /// <summary>
        /// 无
        /// </summary>
        None,
        /// <summary>
        /// 单下划线
        /// </summary>
        Single,
        /// <summary>
        /// 双下划线
        /// </summary>
        Double,
        /// <summary>
        /// 粗下划线
        /// </summary>
        Heavy,
        /// <summary>
        /// 点状下划线
        /// </summary>
        Dotted,
        /// <summary>
        /// 虚线下划线
        /// </summary>
        Dashed,
        /// <summary>
        /// 点划线下划线
        /// </summary>
        DashDot,
        /// <summary>
        /// 双点划线下划线
        /// </summary>
        DashDotDot,
        /// <summary>
        /// 波浪线下划线
        /// </summary>
        Wavy
    }

    #endregion

    #region 文档属性设置相关类

    /// <summary>
    /// 文档属性设置配置类
    /// </summary>
    public class DocumentPropertiesSettings
    {
        /// <summary>
        /// 是否启用文档属性设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 删除设置
        /// </summary>
        public DocumentPropertiesDeletionSettings DeletionSettings { get; set; } = new DocumentPropertiesDeletionSettings();

        /// <summary>
        /// 基本信息属性设置
        /// </summary>
        public BasicPropertiesSettings BasicProperties { get; set; } = new BasicPropertiesSettings();

        /// <summary>
        /// 统计属性设置
        /// </summary>
        public StatisticsPropertiesSettings StatisticsProperties { get; set; } = new StatisticsPropertiesSettings();

        /// <summary>
        /// 时间属性设置
        /// </summary>
        public TimePropertiesSettings TimeProperties { get; set; } = new TimePropertiesSettings();

        /// <summary>
        /// 自定义属性设置
        /// </summary>
        public CustomPropertiesSettings CustomProperties { get; set; } = new CustomPropertiesSettings();
    }

    /// <summary>
    /// 文档属性删除设置
    /// </summary>
    public class DocumentPropertiesDeletionSettings
    {
        /// <summary>
        /// 是否删除所有内置属性
        /// </summary>
        public bool DeleteAllBuiltInProperties { get; set; } = false;

        /// <summary>
        /// 是否删除所有自定义属性
        /// </summary>
        public bool DeleteAllCustomProperties { get; set; } = false;

        /// <summary>
        /// 是否删除基本信息属性
        /// </summary>
        public bool DeleteBasicProperties { get; set; } = false;

        /// <summary>
        /// 是否删除统计属性
        /// </summary>
        public bool DeleteStatisticsProperties { get; set; } = false;

        /// <summary>
        /// 是否删除时间属性
        /// </summary>
        public bool DeleteTimeProperties { get; set; } = false;

        /// <summary>
        /// 指定删除的自定义属性名称列表
        /// </summary>
        public List<string> CustomPropertiesToDelete { get; set; } = new List<string>();
    }

    /// <summary>
    /// 基本信息属性设置
    /// </summary>
    public class BasicPropertiesSettings
    {
        /// <summary>
        /// 是否启用基本信息属性设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = "";

        /// <summary>
        /// 是否设置标题
        /// </summary>
        public bool SetTitle { get; set; } = false;

        /// <summary>
        /// 作者
        /// </summary>
        public string Author { get; set; } = "";

        /// <summary>
        /// 是否设置作者
        /// </summary>
        public bool SetAuthor { get; set; } = false;

        /// <summary>
        /// 主题
        /// </summary>
        public string Subject { get; set; } = "";

        /// <summary>
        /// 是否设置主题
        /// </summary>
        public bool SetSubject { get; set; } = false;

        /// <summary>
        /// 关键词
        /// </summary>
        public string Keywords { get; set; } = "";

        /// <summary>
        /// 是否设置关键词
        /// </summary>
        public bool SetKeywords { get; set; } = false;

        /// <summary>
        /// 描述/注释
        /// </summary>
        public string Comments { get; set; } = "";

        /// <summary>
        /// 是否设置描述/注释
        /// </summary>
        public bool SetComments { get; set; } = false;

        /// <summary>
        /// 类别
        /// </summary>
        public string Category { get; set; } = "";

        /// <summary>
        /// 是否设置类别
        /// </summary>
        public bool SetCategory { get; set; } = false;

        /// <summary>
        /// 公司
        /// </summary>
        public string Company { get; set; } = "";

        /// <summary>
        /// 是否设置公司
        /// </summary>
        public bool SetCompany { get; set; } = false;

        /// <summary>
        /// 管理者
        /// </summary>
        public string Manager { get; set; } = "";

        /// <summary>
        /// 是否设置管理者
        /// </summary>
        public bool SetManager { get; set; } = false;
    }

    /// <summary>
    /// 统计属性设置
    /// </summary>
    public class StatisticsPropertiesSettings
    {
        /// <summary>
        /// 是否启用统计属性设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 是否更新幻灯片数量统计
        /// </summary>
        public bool UpdateSlideCount { get; set; } = false;

        /// <summary>
        /// 是否更新隐藏幻灯片数统计
        /// </summary>
        public bool UpdateHiddenSlideCount { get; set; } = false;

        /// <summary>
        /// 是否更新备注页数统计
        /// </summary>
        public bool UpdateNotesCount { get; set; } = false;

        /// <summary>
        /// 是否更新段落数量统计
        /// </summary>
        public bool UpdateParagraphCount { get; set; } = false;

        /// <summary>
        /// 是否更新字数统计
        /// </summary>
        public bool UpdateWordCount { get; set; } = false;

        /// <summary>
        /// 是否更新多媒体剪辑数统计
        /// </summary>
        public bool UpdateMultimediaClipCount { get; set; } = false;
    }

    /// <summary>
    /// 时间属性设置
    /// </summary>
    public class TimePropertiesSettings
    {
        /// <summary>
        /// 是否启用时间属性设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否设置创建时间
        /// </summary>
        public bool SetCreatedTime { get; set; } = false;

        /// <summary>
        /// 最后保存时间
        /// </summary>
        public DateTime LastSavedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否设置最后保存时间
        /// </summary>
        public bool SetLastSavedTime { get; set; } = false;

        /// <summary>
        /// 最后保存者
        /// </summary>
        public string LastSavedBy { get; set; } = "";

        /// <summary>
        /// 是否设置最后保存者
        /// </summary>
        public bool SetLastSavedBy { get; set; } = false;

        /// <summary>
        /// 最后打印时间
        /// </summary>
        public DateTime LastPrintedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否设置最后打印时间
        /// </summary>
        public bool SetLastPrintedTime { get; set; } = false;

        /// <summary>
        /// 总编辑时间（分钟）
        /// </summary>
        public int TotalEditingTimeMinutes { get; set; } = 0;

        /// <summary>
        /// 是否设置总编辑时间
        /// </summary>
        public bool SetTotalEditingTime { get; set; } = false;
    }

    /// <summary>
    /// 自定义属性设置
    /// </summary>
    public class CustomPropertiesSettings
    {
        /// <summary>
        /// 是否启用自定义属性设置
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 自定义属性列表
        /// </summary>
        public List<CustomPropertyInfo> CustomProperties { get; set; } = new List<CustomPropertyInfo>();
    }

    /// <summary>
    /// 自定义属性信息
    /// </summary>
    public class CustomPropertyInfo
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 属性值
        /// </summary>
        public string Value { get; set; } = "";

        /// <summary>
        /// 属性类型（Text, Number, Date, Boolean）
        /// </summary>
        public string PropertyType { get; set; } = "Text";

        /// <summary>
        /// 是否启用此属性
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    #endregion

    #region 文件名替换设置相关类

    /// <summary>
    /// 文件名替换设置配置类
    /// </summary>
    public class FilenameReplacementSettings
    {
        /// <summary>
        /// 文件名模式替换设置
        /// </summary>
        public FilenamePatternReplacementSettings PatternReplacement { get; set; } = new FilenamePatternReplacementSettings();



        /// <summary>
        /// 冲突处理设置
        /// </summary>
        public FilenameConflictHandlingSettings ConflictHandling { get; set; } = new FilenameConflictHandlingSettings();
    }

    /// <summary>
    /// 文件名模式替换设置
    /// </summary>
    public class FilenamePatternReplacementSettings
    {
        /// <summary>
        /// 是否启用文件名模式替换
        /// </summary>
        public bool IsEnabled { get; set; } = false;

        /// <summary>
        /// 文件名模式替换规则列表
        /// </summary>
        public List<FilenamePatternReplacementRule> ReplacementRules { get; set; } = new List<FilenamePatternReplacementRule>();
    }



    /// <summary>
    /// 文件名冲突处理设置
    /// </summary>
    public class FilenameConflictHandlingSettings
    {
        /// <summary>
        /// 冲突处理方式
        /// </summary>
        public FilenameConflictHandlingType ConflictHandlingType { get; set; } = FilenameConflictHandlingType.AutoRename;

        /// <summary>
        /// 自动重命名格式
        /// </summary>
        public string AutoRenameFormat { get; set; } = "{filename}_{index}";

        /// <summary>
        /// 是否覆盖现有文件
        /// </summary>
        public bool OverwriteExisting { get; set; } = false;

        /// <summary>
        /// 是否跳过冲突文件
        /// </summary>
        public bool SkipConflicts { get; set; } = false;
    }

    /// <summary>
    /// 文件名替换规则
    /// </summary>
    public class FilenamePatternReplacementRule
    {
        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string RuleName { get; set; } = "";

        /// <summary>
        /// 匹配模式类型
        /// </summary>
        public FilenameMatchType MatchType { get; set; } = FilenameMatchType.Wildcard;

        /// <summary>
        /// 源文本（要替换的文本或正则表达式）
        /// </summary>
        public string SourcePattern { get; set; } = "";

        /// <summary>
        /// 目标文本（替换后的文本）
        /// </summary>
        public string TargetPattern { get; set; } = "";

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 是否包含文件扩展名
        /// </summary>
        public bool IncludeExtension { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }



    /// <summary>
    /// 文件名匹配类型
    /// </summary>
    public enum FilenameMatchType
    {
        /// <summary>
        /// 文本替换
        /// </summary>
        Wildcard,
        /// <summary>
        /// 正则匹配
        /// </summary>
        Regex
    }

    /// <summary>
    /// 文件名冲突处理类型
    /// </summary>
    public enum FilenameConflictHandlingType
    {
        /// <summary>
        /// 自动重命名
        /// </summary>
        AutoRename,
        /// <summary>
        /// 覆盖现有文件
        /// </summary>
        Overwrite,
        /// <summary>
        /// 跳过冲突文件
        /// </summary>
        Skip,
        /// <summary>
        /// 询问用户
        /// </summary>
        Ask
    }

    #endregion

    #region 处理结果相关类

    /// <summary>
    /// 处理结果类
    /// </summary>
    public class ProcessingResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 处理消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception? Exception { get; set; }

        /// <summary>
        /// 处理的项目数量
        /// </summary>
        public int ProcessedCount { get; set; } = 0;

        /// <summary>
        /// 成功的项目数量
        /// </summary>
        public int SuccessCount { get; set; } = 0;

        /// <summary>
        /// 失败的项目数量
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 处理开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 处理耗时
        /// </summary>
        public TimeSpan ElapsedTime => EndTime?.Subtract(StartTime) ?? DateTime.Now.Subtract(StartTime);

        /// <summary>
        /// 附加数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    #endregion

    #region 非法词设置相关类

    /// <summary>
    /// 文件名非法词设置配置类
    /// </summary>
    public class FilenameIllegalWordsSettings
    {
        /// <summary>
        /// 是否启用文件名非法词过滤
        /// </summary>
        public bool EnableFilenameIllegalWordsFilter { get; set; } = false;

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 是否使用正则表达式
        /// </summary>
        public bool UseRegex { get; set; } = false;

        /// <summary>
        /// 是否匹配整个单词
        /// </summary>
        public bool MatchWholeWords { get; set; } = false;

        /// <summary>
        /// 非法词列表
        /// </summary>
        public List<string> IllegalWords { get; set; } = new List<string>();

        /// <summary>
        /// 替换动作：0=删除文件, 1=重命名文件, 2=跳过文件
        /// </summary>
        public int ReplacementAction { get; set; } = 0;

        /// <summary>
        /// 替换文本（当ReplacementAction=1时使用）
        /// </summary>
        public string ReplacementText { get; set; } = "[已删除]";

        /// <summary>
        /// 是否记录非法词日志
        /// </summary>
        public bool LogIllegalWords { get; set; } = true;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = "文件名非法词过滤配置";
    }

    /// <summary>
    /// 内容非法词设置配置类
    /// </summary>
    public class ContentIllegalWordsSettings
    {
        /// <summary>
        /// 是否启用内容非法词过滤
        /// </summary>
        public bool EnableContentIllegalWordsFilter { get; set; } = false;

        /// <summary>
        /// 是否区分大小写
        /// </summary>
        public bool CaseSensitive { get; set; } = false;

        /// <summary>
        /// 是否使用正则表达式
        /// </summary>
        public bool UseRegex { get; set; } = false;

        /// <summary>
        /// 是否匹配整个单词
        /// </summary>
        public bool MatchWholeWords { get; set; } = true;

        /// <summary>
        /// 是否搜索标题
        /// </summary>
        public bool SearchInTitles { get; set; } = true;

        /// <summary>
        /// 是否搜索内容
        /// </summary>
        public bool SearchInContent { get; set; } = true;

        /// <summary>
        /// 是否搜索备注
        /// </summary>
        public bool SearchInNotes { get; set; } = false;

        /// <summary>
        /// 是否搜索注释
        /// </summary>
        public bool SearchInComments { get; set; } = false;

        /// <summary>
        /// 非法词列表
        /// </summary>
        public List<string> IllegalWords { get; set; } = new List<string>();

        /// <summary>
        /// 替换动作：0=删除整个文本框, 1=替换非法词, 2=删除包含非法词的段落
        /// </summary>
        public int ReplacementAction { get; set; } = 1;

        /// <summary>
        /// 替换文本（当ReplacementAction=1时使用）
        /// </summary>
        public string ReplacementText { get; set; } = "***";

        /// <summary>
        /// 是否高亮显示非法词
        /// </summary>
        public bool HighlightIllegalWords { get; set; } = true;

        /// <summary>
        /// 是否记录非法词日志
        /// </summary>
        public bool LogIllegalWords { get; set; } = true;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = "内容非法词过滤配置";
    }

    #endregion

}
